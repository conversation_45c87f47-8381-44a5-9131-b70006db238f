# 社交媒体数据获取系统

这是一个专为AI对冲基金系统设计的社交媒体数据获取工具，能够从Reddit、Twitter等平台获取股票相关的帖子和讨论，并按日期保存为JSON格式，供`social_media_analyst`进行分析。

## 功能特点

### 🔗 多平台支持
- **Reddit**: 从投资相关的subreddit获取帖子
- **Twitter/X**: 获取股票相关的推文和讨论
- **可扩展**: 易于添加新的社交媒体平台

### 📊 智能数据处理
- 按日期自动分类保存
- 支持多种数据格式输出
- 自动去重和内容过滤
- 情感分析预处理

### ⚙️ 灵活配置
- 可配置的搜索关键词
- 平台特定的设置
- API限制管理
- 数据存储自定义

## 安装依赖

```bash
pip install praw tweepy requests python-dotenv
```

## 配置设置

### 1. API密钥配置

在`.env`文件中添加以下配置：

```bash
# Reddit API配置
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=SocialMediaScraper/1.0

# Twitter API配置
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
```

### 2. 获取API密钥

#### Reddit API
1. 访问 https://www.reddit.com/prefs/apps
2. 点击"Create App"或"Create Another App"
3. 选择"script"类型
4. 获取Client ID和Client Secret

#### Twitter API
1. 访问 https://developer.twitter.com/
2. 申请开发者账户
3. 创建应用程序
4. 获取Bearer Token

## 使用方法

### 命令行工具

```bash
# 检查配置状态
python scripts/scrape_social_media.py --check-config

# 获取单个股票特定日期的数据
python scripts/scrape_social_media.py --ticker NVDA --date 2024-01-15

# 获取多个股票日期范围内的数据
python scripts/scrape_social_media.py --ticker AAPL MSFT --start-date 2024-01-01 --end-date 2024-01-07

# 只获取Reddit数据
python scripts/scrape_social_media.py --ticker TSLA --date 2024-01-15 --platforms reddit

# 查看数据摘要
python scripts/scrape_social_media.py --ticker NVDA --summary

# 试运行（不实际获取数据）
python scripts/scrape_social_media.py --ticker AAPL --date 2024-01-15 --dry-run
```

### Python API

```python
from src.tools.social_media_scraper import SocialMediaScraper
from src.tools.local_social_media_reader import get_local_social_media_data

# 创建爬虫实例
scraper = SocialMediaScraper()

# 获取数据
posts = scraper.scrape_all_platforms("NVDA", "2024-01-15", limit_per_platform=50)

# 保存数据
scraper.save_posts_by_date(posts, "NVDA", "2024-01-15")

# 读取已保存的数据
saved_posts = get_local_social_media_data("NVDA", "2024-01-15")
```

## 数据结构

### 输出文件结构
```
social_media_data/
├── NVDA_social_media/
│   ├── reddit_2024-01-15.json
│   ├── twitter_2024-01-15.json
│   └── all_platforms_2024-01-15.json
├── AAPL_social_media/
│   ├── reddit_2024-01-15.json
│   └── twitter_2024-01-15.json
└── ...
```

### 数据字段说明
```json
{
  "platform": "reddit",
  "post_id": "abc123",
  "title": "帖子标题",
  "content": "帖子内容",
  "author": "用户名",
  "created_time": "2024-01-15T10:30:00",
  "url": "https://reddit.com/r/stocks/comments/abc123",
  "upvotes": 150,
  "comments_count": 25,
  "ticker": "NVDA",
  "sentiment": "positive",
  "engagement_score": 200.5,
  "source_subreddit": "stocks",
  "hashtags": ["#NVDA", "#AI"]
}
```

## 配置文件

系统会自动生成`social_media_config.json`配置文件，包含：

- 平台设置
- 搜索关键词
- 数据存储配置
- 内容过滤规则

可以手动编辑此文件来自定义设置。

## 与social_media_analyst集成

获取的数据可以直接被`social_media_analyst`使用：

```python
from src.tools.local_social_media_reader import LocalSocialMediaReader

# 在social_media_analyst中使用
reader = LocalSocialMediaReader()
posts = reader.read_multi_platform_data("NVDA", "2024-01-15")

# 转换为分析所需的格式
social_data = {
    "posts_data": {
        "total_posts": len(posts),
        "reddit_posts": [p for p in posts if p.platform == 'reddit'],
        "twitter_posts": [p for p in posts if p.platform == 'twitter'],
        "sentiment_distribution": calculate_sentiment_distribution(posts),
        "engagement_metrics": calculate_engagement_metrics(posts)
    }
}
```

## 注意事项

### API限制
- **Reddit**: 免费账户每分钟60次请求
- **Twitter**: 免费账户每月300次请求
- 建议设置适当的延迟避免触发限制

### 数据质量
- 自动过滤垃圾内容和机器人账户
- 支持多语言但默认只获取英文内容
- 包含基础的情感分析预处理

### 合规性
- 遵守各平台的API使用条款
- 仅用于研究和教育目的
- 不存储个人敏感信息

## 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   python scripts/scrape_social_media.py --check-config
   ```

2. **网络连接问题**
   - 检查网络连接
   - 确认API服务状态

3. **数据目录权限**
   - 确保有写入权限
   - 检查磁盘空间

### 日志查看
系统会输出详细的日志信息，包括：
- API调用状态
- 数据获取进度
- 错误信息和建议

## 扩展开发

### 添加新平台
1. 在`SocialMediaScraper`类中添加新的获取方法
2. 更新配置文件中的平台设置
3. 实现相应的API客户端初始化

### 自定义数据处理
1. 修改`SocialMediaPost`数据结构
2. 添加新的内容过滤规则
3. 实现自定义的情感分析逻辑

## 许可证

本项目仅供教育和研究目的使用。请遵守相关平台的API使用条款和数据使用政策。
