"""
社交媒体数据获取脚本
支持从多个社交媒体平台获取股票相关帖子并按日期保存为JSON格式
"""

import os
import json
import requests
import praw
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
import time
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class SocialMediaPost:
    """社交媒体帖子数据结构"""
    platform: str  # 平台名称 (reddit, twitter, etc.)
    post_id: str  # 帖子ID
    title: str  # 标题
    content: str  # 内容
    author: str  # 作者
    created_time: str  # 创建时间 (ISO格式)
    url: str  # 帖子链接
    upvotes: int  # 点赞数/支持数
    comments_count: int  # 评论数
    ticker: str  # 相关股票代码
    sentiment: Optional[str] = None  # 情感分析结果
    engagement_score: Optional[float] = None  # 参与度评分
    source_subreddit: Optional[str] = None  # Reddit子版块
    hashtags: Optional[List[str]] = None  # 标签列表


class SocialMediaScraper:
    """社交媒体数据获取器"""
    
    def __init__(self, output_dir: str = "social_media_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 初始化Reddit API客户端
        self.reddit_client = self._init_reddit()

        # 股票相关关键词映射
        self.ticker_keywords = {
            'AAPL': ['Apple', 'AAPL', 'iPhone', 'iPad', 'Mac', 'Tim Cook'],
            'MSFT': ['Microsoft', 'MSFT', 'Windows', 'Azure', 'Office', 'Satya Nadella'],
            'NVDA': ['NVIDIA', 'NVDA', 'GPU', 'AI chip', 'Jensen Huang', 'GeForce'],
            'TSLA': ['Tesla', 'TSLA', 'Elon Musk', 'Model 3', 'Model Y', 'Cybertruck'],
            'GOOGL': ['Google', 'GOOGL', 'Alphabet', 'YouTube', 'Android', 'Sundar Pichai'],
            'AMZN': ['Amazon', 'AMZN', 'AWS', 'Prime', 'Jeff Bezos', 'Andy Jassy'],
        }
    
    def _init_reddit(self) -> Optional[praw.Reddit]:
        """初始化Reddit客户端"""
        try:
            client_id = os.getenv('REDDIT_CLIENT_ID')
            client_secret = os.getenv('REDDIT_CLIENT_SECRET')
            user_agent = os.getenv('REDDIT_USER_AGENT', 'SocialMediaScraper/1.0')
            
            if not client_id or not client_secret:
                logger.warning("Reddit API凭据未配置，跳过Reddit数据获取")
                return None
                
            return praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent
            )
        except Exception as e:
            logger.error(f"初始化Reddit客户端失败: {e}")
            return None
    

    
    def scrape_reddit_posts(self, ticker: str, date: str, limit: int = 50) -> List[SocialMediaPost]:
        """从Reddit获取股票相关帖子"""
        if not self.reddit_client:
            return []
        
        posts = []
        keywords = self.ticker_keywords.get(ticker, [ticker])
        
        # 相关的subreddit列表
        subreddits = [
            'stocks', 'investing', 'SecurityAnalysis', 'ValueInvesting',
            'StockMarket', 'wallstreetbets', 'financialindependence',
            'SecurityAnalysis', 'investing_discussion'
        ]
        
        try:
            target_date = datetime.strptime(date, '%Y-%m-%d')
            
            for subreddit_name in subreddits:
                try:
                    subreddit = self.reddit_client.subreddit(subreddit_name)
                    
                    # 搜索相关帖子
                    for keyword in keywords:
                        search_results = subreddit.search(
                            keyword, 
                            sort='new', 
                            time_filter='day',
                            limit=limit // len(subreddits) // len(keywords)
                        )
                        
                        for submission in search_results:
                            post_date = datetime.fromtimestamp(submission.created_utc)
                            
                            # 检查日期是否匹配
                            if post_date.date() == target_date.date():
                                post = SocialMediaPost(
                                    platform='reddit',
                                    post_id=submission.id,
                                    title=submission.title,
                                    content=submission.selftext,
                                    author=str(submission.author) if submission.author else 'deleted',
                                    created_time=post_date.isoformat(),
                                    url=f"https://reddit.com{submission.permalink}",
                                    upvotes=submission.score,
                                    comments_count=submission.num_comments,
                                    ticker=ticker,
                                    source_subreddit=subreddit_name
                                )
                                posts.append(post)
                                
                except Exception as e:
                    logger.error(f"获取subreddit {subreddit_name}数据失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Reddit数据获取失败: {e}")
        
        return posts
    

    
    def scrape_all_platforms(self, ticker: str, date: str, limit_per_platform: int = 50) -> List[SocialMediaPost]:
        """从Reddit平台获取数据"""
        all_posts = []

        logger.info(f"开始获取 {ticker} 在 {date} 的Reddit数据")

        # Reddit数据
        reddit_posts = self.scrape_reddit_posts(ticker, date, limit_per_platform)
        all_posts.extend(reddit_posts)
        logger.info(f"Reddit: 获取到 {len(reddit_posts)} 条帖子")

        # 按时间排序
        all_posts.sort(key=lambda x: x.created_time, reverse=True)

        return all_posts
    
    def save_posts_by_date(self, posts: List[SocialMediaPost], ticker: str, date: str):
        """按日期保存帖子数据"""
        if not posts:
            logger.warning(f"没有数据需要保存 ({ticker} - {date})")
            return

        # 创建股票特定的目录
        ticker_dir = self.output_dir / f"{ticker}_social_media"
        ticker_dir.mkdir(exist_ok=True)

        # 按平台分组保存（目前只有Reddit）
        platforms = {}
        for post in posts:
            platform = post.platform
            if platform not in platforms:
                platforms[platform] = []
            platforms[platform].append(asdict(post))

        # 保存每个平台的数据
        for platform, platform_posts in platforms.items():
            filename = f"{platform}_{date}.json"
            filepath = ticker_dir / filename

            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(platform_posts, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存 {len(platform_posts)} 条 {platform} 数据到 {filepath}")
            except Exception as e:
                logger.error(f"保存数据失败 {filepath}: {e}")

        # 保存汇总数据
        summary_filename = f"reddit_summary_{date}.json"
        summary_filepath = ticker_dir / summary_filename

        try:
            all_posts_dict = [asdict(post) for post in posts]
            with open(summary_filepath, 'w', encoding='utf-8') as f:
                json.dump(all_posts_dict, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存Reddit汇总数据到 {summary_filepath}")
        except Exception as e:
            logger.error(f"保存汇总数据失败 {summary_filepath}: {e}")


def main():
    """主函数 - 示例用法"""
    scraper = SocialMediaScraper()

    # 配置参数
    ticker = "NVDA"
    start_date = "2024-01-01"
    end_date = "2024-01-07"
    limit_per_platform = 30

    # 获取日期范围内的数据
    current_date = datetime.strptime(start_date, '%Y-%m-%d')
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

    while current_date <= end_date_obj:
        date_str = current_date.strftime('%Y-%m-%d')

        # 获取当天的Reddit数据
        posts = scraper.scrape_all_platforms(ticker, date_str, limit_per_platform)

        # 保存数据
        scraper.save_posts_by_date(posts, ticker, date_str)

        # 移动到下一天
        current_date += timedelta(days=1)

        # 添加延迟避免API限制
        time.sleep(2)

    logger.info("Reddit数据获取完成!")


if __name__ == "__main__":
    main()
