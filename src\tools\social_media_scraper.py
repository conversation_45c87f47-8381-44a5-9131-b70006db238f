"""
社交媒体数据获取脚本
支持从多个社交媒体平台获取股票相关帖子并按日期保存为JSON格式
"""

import os
import json
import requests
import praw
import tweepy
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
import time
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class SocialMediaPost:
    """社交媒体帖子数据结构"""
    platform: str  # 平台名称 (reddit, twitter, etc.)
    post_id: str  # 帖子ID
    title: str  # 标题
    content: str  # 内容
    author: str  # 作者
    created_time: str  # 创建时间 (ISO格式)
    url: str  # 帖子链接
    upvotes: int  # 点赞数/支持数
    comments_count: int  # 评论数
    ticker: str  # 相关股票代码
    sentiment: Optional[str] = None  # 情感分析结果
    engagement_score: Optional[float] = None  # 参与度评分
    source_subreddit: Optional[str] = None  # Reddit子版块
    hashtags: Optional[List[str]] = None  # 标签列表


class SocialMediaScraper:
    """社交媒体数据获取器"""
    
    def __init__(self, output_dir: str = "social_media_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化各平台API客户端
        self.reddit_client = self._init_reddit()
        self.twitter_client = self._init_twitter()
        
        # 股票相关关键词映射
        self.ticker_keywords = {
            'AAPL': ['Apple', 'AAPL', 'iPhone', 'iPad', 'Mac', 'Tim Cook'],
            'MSFT': ['Microsoft', 'MSFT', 'Windows', 'Azure', 'Office', 'Satya Nadella'],
            'NVDA': ['NVIDIA', 'NVDA', 'GPU', 'AI chip', 'Jensen Huang', 'GeForce'],
            'TSLA': ['Tesla', 'TSLA', 'Elon Musk', 'Model 3', 'Model Y', 'Cybertruck'],
            'GOOGL': ['Google', 'GOOGL', 'Alphabet', 'YouTube', 'Android', 'Sundar Pichai'],
            'AMZN': ['Amazon', 'AMZN', 'AWS', 'Prime', 'Jeff Bezos', 'Andy Jassy'],
        }
    
    def _init_reddit(self) -> Optional[praw.Reddit]:
        """初始化Reddit客户端"""
        try:
            client_id = os.getenv('REDDIT_CLIENT_ID')
            client_secret = os.getenv('REDDIT_CLIENT_SECRET')
            user_agent = os.getenv('REDDIT_USER_AGENT', 'SocialMediaScraper/1.0')
            
            if not client_id or not client_secret:
                logger.warning("Reddit API凭据未配置，跳过Reddit数据获取")
                return None
                
            return praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent
            )
        except Exception as e:
            logger.error(f"初始化Reddit客户端失败: {e}")
            return None
    
    def _init_twitter(self) -> Optional[tweepy.Client]:
        """初始化Twitter客户端"""
        try:
            bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
            
            if not bearer_token:
                logger.warning("Twitter API凭据未配置，跳过Twitter数据获取")
                return None
                
            return tweepy.Client(bearer_token=bearer_token)
        except Exception as e:
            logger.error(f"初始化Twitter客户端失败: {e}")
            return None
    
    def scrape_reddit_posts(self, ticker: str, date: str, limit: int = 50) -> List[SocialMediaPost]:
        """从Reddit获取股票相关帖子"""
        if not self.reddit_client:
            return []
        
        posts = []
        keywords = self.ticker_keywords.get(ticker, [ticker])
        
        # 相关的subreddit列表
        subreddits = [
            'stocks', 'investing', 'SecurityAnalysis', 'ValueInvesting',
            'StockMarket', 'wallstreetbets', 'financialindependence',
            'SecurityAnalysis', 'investing_discussion'
        ]
        
        try:
            target_date = datetime.strptime(date, '%Y-%m-%d')
            
            for subreddit_name in subreddits:
                try:
                    subreddit = self.reddit_client.subreddit(subreddit_name)
                    
                    # 搜索相关帖子
                    for keyword in keywords:
                        search_results = subreddit.search(
                            keyword, 
                            sort='new', 
                            time_filter='day',
                            limit=limit // len(subreddits) // len(keywords)
                        )
                        
                        for submission in search_results:
                            post_date = datetime.fromtimestamp(submission.created_utc)
                            
                            # 检查日期是否匹配
                            if post_date.date() == target_date.date():
                                post = SocialMediaPost(
                                    platform='reddit',
                                    post_id=submission.id,
                                    title=submission.title,
                                    content=submission.selftext,
                                    author=str(submission.author) if submission.author else 'deleted',
                                    created_time=post_date.isoformat(),
                                    url=f"https://reddit.com{submission.permalink}",
                                    upvotes=submission.score,
                                    comments_count=submission.num_comments,
                                    ticker=ticker,
                                    source_subreddit=subreddit_name
                                )
                                posts.append(post)
                                
                except Exception as e:
                    logger.error(f"获取subreddit {subreddit_name}数据失败: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Reddit数据获取失败: {e}")
        
        return posts
    
    def scrape_twitter_posts(self, ticker: str, date: str, limit: int = 50) -> List[SocialMediaPost]:
        """从Twitter获取股票相关推文"""
        if not self.twitter_client:
            return []
        
        posts = []
        keywords = self.ticker_keywords.get(ticker, [ticker])
        
        try:
            target_date = datetime.strptime(date, '%Y-%m-%d')
            start_time = target_date.replace(hour=0, minute=0, second=0)
            end_time = target_date.replace(hour=23, minute=59, second=59)
            
            # 构建搜索查询
            query = ' OR '.join([f'${ticker}', f'#{ticker}'] + keywords)
            query += ' -is:retweet lang:en'  # 排除转推，只要英文
            
            # 搜索推文
            tweets = tweepy.Paginator(
                self.twitter_client.search_recent_tweets,
                query=query,
                start_time=start_time,
                end_time=end_time,
                tweet_fields=['created_at', 'author_id', 'public_metrics', 'entities'],
                max_results=min(limit, 100)
            ).flatten(limit=limit)
            
            for tweet in tweets:
                # 提取标签
                hashtags = []
                if tweet.entities and 'hashtags' in tweet.entities:
                    hashtags = [tag['tag'] for tag in tweet.entities['hashtags']]
                
                post = SocialMediaPost(
                    platform='twitter',
                    post_id=tweet.id,
                    title='',  # Twitter没有标题
                    content=tweet.text,
                    author=str(tweet.author_id),
                    created_time=tweet.created_at.isoformat(),
                    url=f"https://twitter.com/i/status/{tweet.id}",
                    upvotes=tweet.public_metrics['like_count'],
                    comments_count=tweet.public_metrics['reply_count'],
                    ticker=ticker,
                    hashtags=hashtags,
                    engagement_score=self._calculate_twitter_engagement(tweet.public_metrics)
                )
                posts.append(post)
                
        except Exception as e:
            logger.error(f"Twitter数据获取失败: {e}")
        
        return posts
    
    def _calculate_twitter_engagement(self, metrics: Dict) -> float:
        """计算Twitter参与度评分"""
        likes = metrics.get('like_count', 0)
        retweets = metrics.get('retweet_count', 0)
        replies = metrics.get('reply_count', 0)
        quotes = metrics.get('quote_count', 0)
        
        # 加权计算参与度
        engagement = likes * 1 + retweets * 2 + replies * 1.5 + quotes * 1.5
        return engagement
    
    def scrape_all_platforms(self, ticker: str, date: str, limit_per_platform: int = 50) -> List[SocialMediaPost]:
        """从所有平台获取数据"""
        all_posts = []
        
        logger.info(f"开始获取 {ticker} 在 {date} 的社交媒体数据")
        
        # Reddit数据
        reddit_posts = self.scrape_reddit_posts(ticker, date, limit_per_platform)
        all_posts.extend(reddit_posts)
        logger.info(f"Reddit: 获取到 {len(reddit_posts)} 条帖子")
        
        # Twitter数据
        twitter_posts = self.scrape_twitter_posts(ticker, date, limit_per_platform)
        all_posts.extend(twitter_posts)
        logger.info(f"Twitter: 获取到 {len(twitter_posts)} 条推文")
        
        # 按时间排序
        all_posts.sort(key=lambda x: x.created_time, reverse=True)
        
        return all_posts
    
    def save_posts_by_date(self, posts: List[SocialMediaPost], ticker: str, date: str):
        """按日期保存帖子数据"""
        if not posts:
            logger.warning(f"没有数据需要保存 ({ticker} - {date})")
            return
        
        # 创建股票特定的目录
        ticker_dir = self.output_dir / f"{ticker}_social_media"
        ticker_dir.mkdir(exist_ok=True)
        
        # 按平台分组保存
        platforms = {}
        for post in posts:
            platform = post.platform
            if platform not in platforms:
                platforms[platform] = []
            platforms[platform].append(asdict(post))
        
        # 保存每个平台的数据
        for platform, platform_posts in platforms.items():
            filename = f"{platform}_{date}.json"
            filepath = ticker_dir / filename
            
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(platform_posts, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存 {len(platform_posts)} 条 {platform} 数据到 {filepath}")
            except Exception as e:
                logger.error(f"保存数据失败 {filepath}: {e}")
        
        # 保存汇总数据
        summary_filename = f"all_platforms_{date}.json"
        summary_filepath = ticker_dir / summary_filename
        
        try:
            all_posts_dict = [asdict(post) for post in posts]
            with open(summary_filepath, 'w', encoding='utf-8') as f:
                json.dump(all_posts_dict, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存汇总数据到 {summary_filepath}")
        except Exception as e:
            logger.error(f"保存汇总数据失败 {summary_filepath}: {e}")


def main():
    """主函数 - 示例用法"""
    scraper = SocialMediaScraper()
    
    # 配置参数
    ticker = "NVDA"
    start_date = "2024-01-01"
    end_date = "2024-01-07"
    limit_per_platform = 30
    
    # 获取日期范围内的数据
    current_date = datetime.strptime(start_date, '%Y-%m-%d')
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
    
    while current_date <= end_date_obj:
        date_str = current_date.strftime('%Y-%m-%d')
        
        # 获取当天的社交媒体数据
        posts = scraper.scrape_all_platforms(ticker, date_str, limit_per_platform)
        
        # 保存数据
        scraper.save_posts_by_date(posts, ticker, date_str)
        
        # 移动到下一天
        current_date += timedelta(days=1)
        
        # 添加延迟避免API限制
        time.sleep(2)
    
    logger.info("数据获取完成!")


if __name__ == "__main__":
    main()
