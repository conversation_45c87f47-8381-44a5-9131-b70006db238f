#!/usr/bin/env python3
"""
社交媒体数据获取系统测试脚本
用于验证整个系统的功能是否正常
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.tools.social_media_scraper import SocialMediaScraper
from src.config.social_media_config import SocialMediaConfig
from src.tools.local_social_media_reader import LocalSocialMediaReader


def test_configuration():
    """测试配置系统"""
    print("🔧 测试配置系统...")
    
    try:
        config = SocialMediaConfig()
        config.print_status()
        
        # 测试配置方法
        reddit_config = config.get_platform_config("reddit")
        assert reddit_config is not None, "Reddit配置获取失败"
        
        keywords = config.get_search_keywords("reddit", "NVDA")
        assert len(keywords) > 0, "关键词获取失败"
        
        subreddits = config.get_subreddits()
        assert len(subreddits) > 0, "Subreddit列表获取失败"
        
        print("✅ 配置系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False


def test_data_structure():
    """测试数据结构"""
    print("\n📊 测试数据结构...")
    
    try:
        from src.tools.social_media_scraper import SocialMediaPost
        
        # 创建测试帖子
        test_post = SocialMediaPost(
            platform="reddit",
            post_id="test123",
            title="Test Post",
            content="This is a test post about NVDA",
            author="test_user",
            created_time="2024-01-15T10:30:00",
            url="https://reddit.com/test",
            upvotes=100,
            comments_count=20,
            ticker="NVDA",
            sentiment="positive",
            engagement_score=150.0,
            source_subreddit="stocks"
        )
        
        # 测试序列化
        from dataclasses import asdict
        post_dict = asdict(test_post)
        assert post_dict["platform"] == "reddit", "数据序列化失败"
        
        print("✅ 数据结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False


def test_mock_data_generation():
    """测试模拟数据生成"""
    print("\n🎭 测试模拟数据生成...")
    
    try:
        from src.tools.social_media_scraper import SocialMediaPost
        from dataclasses import asdict
        import json
        
        # 生成模拟数据
        mock_posts = []
        
        # Reddit模拟数据
        reddit_post = SocialMediaPost(
            platform="reddit",
            post_id="mock_reddit_1",
            title="NVDA earnings discussion",
            content="What do you think about NVDA's latest earnings? The AI chip demand is through the roof!",
            author="investor123",
            created_time="2024-01-15T09:30:00",
            url="https://reddit.com/r/stocks/comments/mock1",
            upvotes=250,
            comments_count=45,
            ticker="NVDA",
            sentiment="bullish",
            engagement_score=295.0,
            source_subreddit="stocks"
        )
        mock_posts.append(reddit_post)
        
        # Twitter模拟数据
        twitter_post = SocialMediaPost(
            platform="twitter",
            post_id="mock_twitter_1",
            title="",
            content="$NVDA breaking new highs! AI revolution is just getting started 🚀 #NVDA #AI",
            author="tech_trader",
            created_time="2024-01-15T10:15:00",
            url="https://twitter.com/i/status/mock1",
            upvotes=180,
            comments_count=25,
            ticker="NVDA",
            sentiment="bullish",
            engagement_score=230.0,
            hashtags=["NVDA", "AI"]
        )
        mock_posts.append(twitter_post)
        
        # 保存模拟数据
        output_dir = Path("test_social_media_data")
        output_dir.mkdir(exist_ok=True)
        
        ticker_dir = output_dir / "NVDA_social_media"
        ticker_dir.mkdir(exist_ok=True)
        
        # 按平台保存
        platforms = {}
        for post in mock_posts:
            platform = post.platform
            if platform not in platforms:
                platforms[platform] = []
            platforms[platform].append(asdict(post))
        
        for platform, platform_posts in platforms.items():
            filename = f"{platform}_2024-01-15.json"
            filepath = ticker_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(platform_posts, f, ensure_ascii=False, indent=2)
        
        # 保存汇总数据
        all_posts_dict = [asdict(post) for post in mock_posts]
        summary_filepath = ticker_dir / "all_platforms_2024-01-15.json"
        
        with open(summary_filepath, 'w', encoding='utf-8') as f:
            json.dump(all_posts_dict, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 模拟数据生成成功，保存到: {ticker_dir}")
        print(f"   - 生成了 {len(mock_posts)} 条模拟帖子")
        return True
        
    except Exception as e:
        print(f"❌ 模拟数据生成失败: {e}")
        return False


def test_data_reader():
    """测试数据读取器"""
    print("\n📖 测试数据读取器...")
    
    try:
        reader = LocalSocialMediaReader("test_social_media_data")
        
        # 测试读取数据
        posts = reader.read_multi_platform_data("NVDA", "2024-01-15")
        assert len(posts) > 0, "数据读取失败"
        
        # 测试数据摘要
        summary = reader.get_data_summary("NVDA")
        assert "ticker" in summary, "数据摘要生成失败"
        
        # 测试可用日期
        dates = reader.get_available_dates("NVDA")
        assert len(dates) > 0, "可用日期获取失败"
        
        print(f"✅ 数据读取器测试通过")
        print(f"   - 读取到 {len(posts)} 条帖子")
        print(f"   - 可用日期: {dates}")
        return True
        
    except Exception as e:
        print(f"❌ 数据读取器测试失败: {e}")
        return False


def test_integration_with_analyst():
    """测试与social_media_analyst的集成"""
    print("\n🔗 测试与social_media_analyst的集成...")
    
    try:
        from src.tools.local_social_media_reader import get_local_social_media_data
        
        # 获取数据
        posts = get_local_social_media_data(
            ticker="NVDA",
            date="2024-01-15",
            platforms=["reddit", "twitter"],
            limit=50,
            base_directory="test_social_media_data"
        )
        
        assert len(posts) > 0, "集成数据获取失败"
        
        # 模拟social_media_analyst的数据处理
        social_data = {
            "posts_data": {
                "total_posts": len(posts),
                "reddit_posts": [p for p in posts if p.platform == 'reddit'],
                "twitter_posts": [p for p in posts if p.platform == 'twitter'],
                "sentiment_distribution": {
                    "bullish": len([p for p in posts if p.sentiment == 'bullish']),
                    "bearish": len([p for p in posts if p.sentiment == 'bearish']),
                    "neutral": len([p for p in posts if p.sentiment == 'neutral'])
                },
                "engagement_metrics": {
                    "total_upvotes": sum(p.upvotes for p in posts),
                    "total_comments": sum(p.comments_count for p in posts),
                    "avg_engagement": sum(p.engagement_score or 0 for p in posts) / len(posts)
                }
            }
        }
        
        print(f"✅ 集成测试通过")
        print(f"   - 处理了 {social_data['posts_data']['total_posts']} 条帖子")
        print(f"   - Reddit: {len(social_data['posts_data']['reddit_posts'])} 条")
        print(f"   - Twitter: {len(social_data['posts_data']['twitter_posts'])} 条")
        print(f"   - 平均参与度: {social_data['posts_data']['engagement_metrics']['avg_engagement']:.1f}")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        import shutil
        test_dir = Path("test_social_media_data")
        
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print("✅ 测试数据清理完成")
        
    except Exception as e:
        print(f"⚠️  测试数据清理失败: {e}")


def main():
    """主测试函数"""
    print("🚀 开始社交媒体数据获取系统测试")
    print("=" * 50)
    
    tests = [
        ("配置系统", test_configuration),
        ("数据结构", test_data_structure),
        ("模拟数据生成", test_mock_data_generation),
        ("数据读取器", test_data_reader),
        ("分析师集成", test_integration_with_analyst),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖")
    
    # 清理测试数据
    cleanup_test_data()
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
