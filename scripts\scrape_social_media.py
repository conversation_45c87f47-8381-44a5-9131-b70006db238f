#!/usr/bin/env python3
"""
社交媒体数据获取命令行工具
用于批量获取股票相关的社交媒体数据并按日期保存
"""

import argparse
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.tools.social_media_scraper import SocialMediaScraper
from src.config.social_media_config import social_media_config
from src.tools.local_social_media_reader import LocalSocialMediaReader


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="社交媒体数据获取工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 获取NVDA在特定日期的数据
  python scripts/scrape_social_media.py --ticker NVDA --date 2024-01-15

  # 获取多个股票在日期范围内的数据
  python scripts/scrape_social_media.py --ticker AAPL MSFT --start-date 2024-01-01 --end-date 2024-01-07

  # 获取Reddit数据
  python scripts/scrape_social_media.py --ticker TSLA --date 2024-01-15

  # 查看数据摘要
  python scripts/scrape_social_media.py --ticker NVDA --summary

  # 检查配置状态
  python scripts/scrape_social_media.py --check-config
        """
    )
    
    # 基本参数
    parser.add_argument(
        '--ticker', '-t',
        nargs='+',
        help='股票代码列表 (例如: AAPL MSFT NVDA)'
    )
    
    parser.add_argument(
        '--date', '-d',
        help='特定日期 (YYYY-MM-DD格式)'
    )
    
    parser.add_argument(
        '--start-date',
        help='开始日期 (YYYY-MM-DD格式)'
    )
    
    parser.add_argument(
        '--end-date',
        help='结束日期 (YYYY-MM-DD格式)'
    )
    
    # 平台选择（目前只支持Reddit）
    parser.add_argument(
        '--platforms', '-p',
        nargs='+',
        choices=['reddit'],
        default=['reddit'],
        help='要获取数据的平台 (目前只支持: reddit)'
    )
    
    # 数据限制
    parser.add_argument(
        '--limit',
        type=int,
        default=50,
        help='每个平台的数据限制 (默认: 50)'
    )
    
    # 输出目录
    parser.add_argument(
        '--output-dir',
        default='social_media_data',
        help='输出目录 (默认: social_media_data)'
    )
    
    # 延迟设置
    parser.add_argument(
        '--delay',
        type=int,
        default=2,
        help='请求间延迟秒数 (默认: 2)'
    )
    
    # 功能选项
    parser.add_argument(
        '--summary',
        action='store_true',
        help='显示数据摘要信息'
    )
    
    parser.add_argument(
        '--check-config',
        action='store_true',
        help='检查配置状态'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行，不实际获取数据'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    return parser.parse_args()


def validate_date(date_str):
    """验证日期格式"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 格式")


def check_configuration():
    """检查配置状态"""
    print("🔍 检查社交媒体配置状态...")
    social_media_config.print_status()
    
    # 检查API密钥（只检查启用的平台）
    api_status = social_media_config.validate_api_keys()
    enabled_platforms = [platform for platform in api_status.keys()
                        if social_media_config.is_platform_enabled(platform)]
    missing_keys = [platform for platform in enabled_platforms
                   if not api_status.get(platform, False)]

    if missing_keys:
        print(f"\n⚠️  缺少API密钥的启用平台: {', '.join(missing_keys)}")
        print("\n请在.env文件中配置以下环境变量:")

        if 'reddit' in missing_keys:
            print("  - REDDIT_CLIENT_ID")
            print("  - REDDIT_CLIENT_SECRET")
            print("  - REDDIT_USER_AGENT (可选)")

        return False
    
    print("\n✅ 所有配置检查通过!")
    return True


def show_data_summary(tickers):
    """显示数据摘要"""
    reader = LocalSocialMediaReader()
    
    for ticker in tickers:
        print(f"\n📊 {ticker} 数据摘要:")
        summary = reader.get_data_summary(ticker)
        
        if "error" in summary:
            print(f"  ❌ {summary['error']}")
            continue
        
        print(f"  📁 数据目录: {summary['data_directory']}")
        print(f"  📄 总文件数: {summary['total_files']}")
        
        if summary.get('date_range'):
            date_range = summary['date_range']
            print(f"  📅 日期范围: {date_range['start']} 到 {date_range['end']} ({date_range['total_days']} 天)")
        
        print("  🔗 平台文件数:")
        for platform, count in summary['platforms'].items():
            if count > 0:
                print(f"    - {platform.capitalize()}: {count} 个文件")


def scrape_data(args):
    """执行数据获取"""
    scraper = SocialMediaScraper(args.output_dir)
    
    # 确定日期范围
    dates = []
    if args.date:
        dates = [args.date]
    elif args.start_date and args.end_date:
        start_dt = validate_date(args.start_date)
        end_dt = validate_date(args.end_date)
        
        current_date = start_dt
        while current_date <= end_dt:
            dates.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
    else:
        # 默认使用昨天的日期
        yesterday = datetime.now() - timedelta(days=1)
        dates = [yesterday.strftime('%Y-%m-%d')]
    
    print(f"📅 将获取以下日期的数据: {', '.join(dates)}")
    print(f"📈 股票代码: {', '.join(args.ticker)}")
    print(f"🔗 平台: Reddit")
    print(f"📊 数据限制: {args.limit} 条")
    
    if args.dry_run:
        print("\n🔍 试运行模式 - 不会实际获取数据")
        return
    
    # 开始获取数据
    total_tasks = len(args.ticker) * len(dates)
    current_task = 0
    
    for ticker in args.ticker:
        for date in dates:
            current_task += 1
            print(f"\n📥 [{current_task}/{total_tasks}] 获取 {ticker} - {date} 的Reddit数据...")

            try:
                # 获取Reddit数据
                posts = scraper.scrape_all_platforms(ticker, date, args.limit)
                
                if posts:
                    # 保存数据
                    scraper.save_posts_by_date(posts, ticker, date)
                    print(f"✅ 成功获取并保存 {len(posts)} 条数据")
                else:
                    print("⚠️  未获取到数据")
                
                # 添加延迟
                if current_task < total_tasks:
                    print(f"⏱️  等待 {args.delay} 秒...")
                    import time
                    time.sleep(args.delay)
                    
            except Exception as e:
                print(f"❌ 获取数据失败: {e}")
                if args.verbose:
                    import traceback
                    traceback.print_exc()
    
    print(f"\n🎉 数据获取完成! 共处理 {total_tasks} 个任务")


def main():
    """主函数"""
    args = parse_arguments()
    
    # 检查配置
    if args.check_config:
        check_configuration()
        return
    
    # 显示摘要
    if args.summary:
        if not args.ticker:
            print("❌ 请指定股票代码 (--ticker)")
            return
        show_data_summary(args.ticker)
        return
    
    # 验证参数
    if not args.ticker:
        print("❌ 请指定股票代码 (--ticker)")
        return
    
    # 验证日期参数
    if args.date and (args.start_date or args.end_date):
        print("❌ 不能同时指定 --date 和 --start-date/--end-date")
        return
    
    if (args.start_date and not args.end_date) or (args.end_date and not args.start_date):
        print("❌ --start-date 和 --end-date 必须同时指定")
        return
    
    # 验证日期格式
    try:
        if args.date:
            validate_date(args.date)
        if args.start_date:
            validate_date(args.start_date)
        if args.end_date:
            validate_date(args.end_date)
    except argparse.ArgumentTypeError as e:
        print(f"❌ {e}")
        return
    
    # 检查配置
    if not check_configuration():
        print("\n❌ 配置检查失败，请先配置API密钥")
        return
    
    # 执行数据获取
    scrape_data(args)


if __name__ == "__main__":
    main()
