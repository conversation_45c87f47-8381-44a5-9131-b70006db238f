"""
本地社交媒体数据读取器
用于读取已保存的社交媒体数据，供social_media_analyst使用
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SocialMediaPost:
    """社交媒体帖子数据结构"""
    platform: str
    post_id: str
    title: str
    content: str
    author: str
    created_time: str
    url: str
    upvotes: int
    comments_count: int
    ticker: str
    sentiment: Optional[str] = None
    engagement_score: Optional[float] = None
    source_subreddit: Optional[str] = None
    hashtags: Optional[List[str]] = None


class LocalSocialMediaReader:
    """本地社交媒体数据读取器"""
    
    def __init__(self, base_directory: str = "social_media_data"):
        self.base_directory = Path(base_directory)
        
        # 平台文件名模式
        self.platform_patterns = {
            'reddit': 'reddit_{date}.json',
            'summary': 'reddit_summary_{date}.json'
        }
    
    def read_platform_data(self, ticker: str, date: str, platform: str) -> List[SocialMediaPost]:
        """
        读取特定平台的社交媒体数据
        
        Args:
            ticker: 股票代码
            date: 日期 (YYYY-MM-DD格式)
            platform: 平台名称 (reddit, twitter, all)
            
        Returns:
            List[SocialMediaPost]: 社交媒体帖子列表
        """
        ticker_dir = self.base_directory / f"{ticker}_social_media"
        
        if not ticker_dir.exists():
            logger.warning(f"社交媒体数据目录不存在: {ticker_dir}")
            return []
        
        # 获取文件名模式
        file_pattern = self.platform_patterns.get(platform, f"{platform}_{date}.json")
        file_path = ticker_dir / file_pattern.format(date=date)
        
        if not file_path.exists():
            logger.warning(f"社交媒体数据文件不存在: {file_path}")
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            posts = []
            for item in data:
                try:
                    post = SocialMediaPost(
                        platform=item.get('platform', platform),
                        post_id=item.get('post_id', ''),
                        title=item.get('title', ''),
                        content=item.get('content', ''),
                        author=item.get('author', ''),
                        created_time=item.get('created_time', ''),
                        url=item.get('url', ''),
                        upvotes=item.get('upvotes', 0),
                        comments_count=item.get('comments_count', 0),
                        ticker=item.get('ticker', ticker),
                        sentiment=item.get('sentiment'),
                        engagement_score=item.get('engagement_score'),
                        source_subreddit=item.get('source_subreddit'),
                        hashtags=item.get('hashtags')
                    )
                    posts.append(post)
                except Exception as e:
                    logger.error(f"解析帖子数据失败: {e}")
                    continue
            
            logger.info(f"✅ {platform.capitalize()}: 读取到 {len(posts)} 条帖子 ({date})")
            return posts
            
        except Exception as e:
            logger.error(f"❌ 读取{platform}数据失败: {e}")
            return []
    
    def read_reddit_data(self, ticker: str, date: str) -> List[SocialMediaPost]:
        """读取Reddit数据"""
        return self.read_platform_data(ticker, date, 'reddit')
    
    def read_reddit_summary_data(self, ticker: str, date: str) -> List[SocialMediaPost]:
        """读取Reddit汇总数据"""
        return self.read_platform_data(ticker, date, 'summary')
    
    def read_multi_platform_data(
        self,
        ticker: str,
        date: str,
        platforms: Optional[List[str]] = None,
        limit_per_platform: int = 50
    ) -> List[SocialMediaPost]:
        """
        读取多个平台的数据
        
        Args:
            ticker: 股票代码
            date: 日期
            platforms: 平台列表，默认为['reddit', 'twitter']
            limit_per_platform: 每个平台的数据限制
            
        Returns:
            List[SocialMediaPost]: 合并的帖子列表
        """
        if platforms is None:
            platforms = ['reddit']
        
        all_posts = []
        
        for platform in platforms:
            platform_posts = self.read_platform_data(ticker, date, platform)
            
            # 限制每个平台的数据量
            if len(platform_posts) > limit_per_platform:
                # 按参与度排序，取前N条
                platform_posts.sort(
                    key=lambda x: (x.engagement_score or 0) + x.upvotes, 
                    reverse=True
                )
                platform_posts = platform_posts[:limit_per_platform]
            
            all_posts.extend(platform_posts)
        
        # 按时间排序
        all_posts.sort(key=lambda x: x.created_time, reverse=True)
        
        return all_posts
    
    def read_date_range_data(
        self,
        ticker: str,
        start_date: str,
        end_date: str,
        platforms: Optional[List[str]] = None,
        limit_per_day: int = 20
    ) -> List[SocialMediaPost]:
        """
        读取日期范围内的数据
        
        Args:
            ticker: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            platforms: 平台列表
            limit_per_day: 每天的数据限制
            
        Returns:
            List[SocialMediaPost]: 时间范围内的帖子列表
        """
        if platforms is None:
            platforms = ['reddit']
        
        all_posts = []
        
        # 生成日期列表
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        current_date = start_dt
        while current_date <= end_dt:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 读取当天数据
            day_posts = self.read_multi_platform_data(
                ticker, date_str, platforms, limit_per_day // len(platforms)
            )
            
            # 限制当天数据量
            if len(day_posts) > limit_per_day:
                day_posts = day_posts[:limit_per_day]
            
            all_posts.extend(day_posts)
            current_date += timedelta(days=1)
        
        # 按时间排序
        all_posts.sort(key=lambda x: x.created_time, reverse=True)
        
        return all_posts
    
    def get_available_dates(self, ticker: str, platform: Optional[str] = None) -> List[str]:
        """
        获取可用的数据日期列表
        
        Args:
            ticker: 股票代码
            platform: 平台名称，None表示所有平台
            
        Returns:
            List[str]: 可用日期列表
        """
        ticker_dir = self.base_directory / f"{ticker}_social_media"
        
        if not ticker_dir.exists():
            return []
        
        dates = set()
        
        # 扫描文件
        for file_path in ticker_dir.glob("*.json"):
            filename = file_path.name
            
            # 提取日期
            if platform:
                pattern = self.platform_patterns.get(platform, f"{platform}_*.json")
                if pattern.replace('{date}', '') in filename:
                    # 提取日期部分
                    date_part = filename.replace(pattern.replace('{date}', ''), '').replace('.json', '')
                    if self._is_valid_date(date_part):
                        dates.add(date_part)
            else:
                # 从任何文件名中提取日期
                parts = filename.replace('.json', '').split('_')
                for part in parts:
                    if self._is_valid_date(part):
                        dates.add(part)
        
        return sorted(list(dates))
    
    def _is_valid_date(self, date_str: str) -> bool:
        """检查日期字符串是否有效"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    def get_data_summary(self, ticker: str) -> Dict[str, Any]:
        """
        获取数据摘要信息
        
        Args:
            ticker: 股票代码
            
        Returns:
            Dict: 数据摘要
        """
        ticker_dir = self.base_directory / f"{ticker}_social_media"
        
        if not ticker_dir.exists():
            return {"error": "数据目录不存在"}
        
        summary = {
            "ticker": ticker,
            "data_directory": str(ticker_dir),
            "platforms": {},
            "date_range": {},
            "total_files": 0
        }
        
        # 统计各平台文件
        for platform in ['reddit', 'summary']:
            platform_files = list(ticker_dir.glob(f"{platform}_*.json"))
            summary["platforms"][platform] = len(platform_files)
            summary["total_files"] += len(platform_files)
        
        # 获取日期范围
        all_dates = self.get_available_dates(ticker)
        if all_dates:
            summary["date_range"]["start"] = all_dates[0]
            summary["date_range"]["end"] = all_dates[-1]
            summary["date_range"]["total_days"] = len(all_dates)
        
        return summary


# 便捷函数
def get_local_social_media_data(
    ticker: str,
    date: str,
    platforms: Optional[List[str]] = None,
    limit: int = 50,
    base_directory: str = "social_media_data"
) -> List[SocialMediaPost]:
    """
    获取本地社交媒体数据的便捷函数
    
    Args:
        ticker: 股票代码
        date: 日期 (YYYY-MM-DD)
        platforms: 平台列表，默认为['reddit', 'twitter']
        limit: 数据限制
        base_directory: 数据目录
        
    Returns:
        List[SocialMediaPost]: 社交媒体帖子列表
    """
    reader = LocalSocialMediaReader(base_directory)
    return reader.read_multi_platform_data(ticker, date, platforms, limit)


def main():
    """测试函数"""
    reader = LocalSocialMediaReader()
    
    # 测试读取数据
    ticker = "NVDA"
    date = "2024-01-15"
    
    print(f"测试读取 {ticker} 在 {date} 的社交媒体数据...")
    
    # 获取数据摘要
    summary = reader.get_data_summary(ticker)
    print(f"数据摘要: {json.dumps(summary, indent=2, ensure_ascii=False)}")
    
    # 读取多平台数据
    posts = reader.read_multi_platform_data(ticker, date)
    print(f"读取到 {len(posts)} 条帖子")
    
    # 显示前几条
    for i, post in enumerate(posts[:3]):
        print(f"\n帖子 {i+1}:")
        print(f"  平台: {post.platform}")
        print(f"  标题: {post.title[:50]}...")
        print(f"  内容: {post.content[:100]}...")
        print(f"  点赞: {post.upvotes}, 评论: {post.comments_count}")


if __name__ == "__main__":
    main()
