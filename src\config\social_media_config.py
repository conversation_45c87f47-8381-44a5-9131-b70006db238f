"""
社交媒体数据获取配置管理
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class PlatformConfig:
    """平台配置"""
    name: str
    enabled: bool
    api_key_env: Optional[str] = None
    rate_limit_per_hour: int = 100
    max_posts_per_request: int = 50
    subreddits: Optional[List[str]] = None
    search_keywords: Optional[List[str]] = None


class SocialMediaConfig:
    """社交媒体配置管理器"""
    
    def __init__(self, config_file: str = "social_media_config.json"):
        self.config_file = config_file
        self.config = self._load_default_config()
        self.load_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            "platforms": {
                "reddit": {
                    "name": "Reddit",
                    "enabled": True,
                    "api_key_env": "REDDIT_CLIENT_ID",
                    "api_secret_env": "REDDIT_CLIENT_SECRET",
                    "user_agent_env": "REDDIT_USER_AGENT",
                    "rate_limit_per_hour": 60,
                    "max_posts_per_request": 100,
                    "subreddits": [
                        "stocks", "investing", "SecurityAnalysis", "ValueInvesting",
                        "StockMarket", "wallstreetbets", "financialindependence",
                        "SecurityAnalysis", "investing_discussion", "options",
                        "pennystocks", "dividends", "ETFs"
                    ],
                    "search_keywords": {
                        "AAPL": ["Apple", "AAPL", "iPhone", "iPad", "Mac", "Tim Cook", "iOS"],
                        "MSFT": ["Microsoft", "MSFT", "Windows", "Azure", "Office", "Satya Nadella", "Xbox"],
                        "NVDA": ["NVIDIA", "NVDA", "GPU", "AI chip", "Jensen Huang", "GeForce", "RTX"],
                        "TSLA": ["Tesla", "TSLA", "Elon Musk", "Model 3", "Model Y", "Cybertruck", "FSD"],
                        "GOOGL": ["Google", "GOOGL", "Alphabet", "YouTube", "Android", "Sundar Pichai"],
                        "AMZN": ["Amazon", "AMZN", "AWS", "Prime", "Jeff Bezos", "Andy Jassy"],
                        "META": ["Meta", "META", "Facebook", "Instagram", "WhatsApp", "Mark Zuckerberg"],
                        "NFLX": ["Netflix", "NFLX", "streaming", "Reed Hastings"],
                        "AMD": ["AMD", "Ryzen", "EPYC", "Lisa Su", "Radeon"],
                        "INTC": ["Intel", "INTC", "processor", "Pat Gelsinger"]
                    }
                },
                "twitter": {
                    "name": "Twitter/X",
                    "enabled": False,
                    "api_key_env": "TWITTER_BEARER_TOKEN",
                    "rate_limit_per_hour": 300,
                    "max_posts_per_request": 100,
                    "search_keywords": {
                        "AAPL": ["$AAPL", "#AAPL", "Apple", "iPhone", "iPad"],
                        "MSFT": ["$MSFT", "#MSFT", "Microsoft", "Azure", "Windows"],
                        "NVDA": ["$NVDA", "#NVDA", "NVIDIA", "GPU", "AI"],
                        "TSLA": ["$TSLA", "#TSLA", "Tesla", "Elon"],
                        "GOOGL": ["$GOOGL", "#GOOGL", "Google", "Alphabet"],
                        "AMZN": ["$AMZN", "#AMZN", "Amazon", "AWS"],
                        "META": ["$META", "#META", "Meta", "Facebook"],
                        "NFLX": ["$NFLX", "#NFLX", "Netflix"],
                        "AMD": ["$AMD", "#AMD", "AMD", "Ryzen"],
                        "INTC": ["$INTC", "#INTC", "Intel"]
                    }
                }
            },
            "data_storage": {
                "base_directory": "social_media_data",
                "file_format": "json",
                "date_format": "%Y-%m-%d",
                "filename_pattern": "{platform}_{date}.json",
                "summary_filename_pattern": "all_platforms_{date}.json",
                "backup_enabled": True,
                "compression_enabled": False
            },
            "scraping_settings": {
                "default_limit_per_platform": 50,
                "max_concurrent_requests": 3,
                "request_delay_seconds": 2,
                "retry_attempts": 3,
                "timeout_seconds": 30,
                "user_agent": "SocialMediaScraper/1.0 (Educational Purpose)"
            },
            "content_filtering": {
                "min_content_length": 10,
                "max_content_length": 5000,
                "exclude_deleted_users": True,
                "exclude_bot_accounts": True,
                "language_filter": ["en"],
                "spam_keywords": [
                    "crypto scam", "pump and dump", "guaranteed profit",
                    "risk-free", "get rich quick", "investment opportunity"
                ]
            },
            "sentiment_analysis": {
                "enabled": True,
                "provider": "local",  # local, openai, huggingface
                "confidence_threshold": 0.6,
                "batch_size": 10
            }
        }
    
    def load_config(self):
        """从文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    # 合并配置，文件配置优先
                    self._merge_config(self.config, file_config)
                print(f"✅ 已加载配置文件: {self.config_file}")
            except Exception as e:
                print(f"❌ 加载配置文件失败: {e}")
    
    def _merge_config(self, default: Dict, override: Dict):
        """递归合并配置"""
        for key, value in override.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_config(default[key], value)
            else:
                default[key] = value
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
    
    def get_platform_config(self, platform: str) -> Optional[Dict[str, Any]]:
        """获取平台配置"""
        return self.config.get("platforms", {}).get(platform)
    
    def is_platform_enabled(self, platform: str) -> bool:
        """检查平台是否启用"""
        platform_config = self.get_platform_config(platform)
        return platform_config.get("enabled", False) if platform_config else False
    
    def get_search_keywords(self, platform: str, ticker: str) -> List[str]:
        """获取搜索关键词"""
        platform_config = self.get_platform_config(platform)
        if not platform_config:
            return [ticker]
        
        keywords = platform_config.get("search_keywords", {}).get(ticker, [ticker])
        return keywords if isinstance(keywords, list) else [ticker]
    
    def get_subreddits(self) -> List[str]:
        """获取Reddit子版块列表"""
        reddit_config = self.get_platform_config("reddit")
        return reddit_config.get("subreddits", []) if reddit_config else []
    
    def get_data_directory(self, ticker: str) -> Path:
        """获取数据存储目录"""
        base_dir = self.config.get("data_storage", {}).get("base_directory", "social_media_data")
        return Path(base_dir) / f"{ticker}_social_media"
    
    def get_filename(self, platform: str, date: str) -> str:
        """获取文件名"""
        pattern = self.config.get("data_storage", {}).get("filename_pattern", "{platform}_{date}.json")
        return pattern.format(platform=platform, date=date)
    
    def get_summary_filename(self, date: str) -> str:
        """获取汇总文件名"""
        pattern = self.config.get("data_storage", {}).get("summary_filename_pattern", "all_platforms_{date}.json")
        return pattern.format(date=date)
    
    def get_scraping_settings(self) -> Dict[str, Any]:
        """获取爬取设置"""
        return self.config.get("scraping_settings", {})
    
    def get_content_filtering_settings(self) -> Dict[str, Any]:
        """获取内容过滤设置"""
        return self.config.get("content_filtering", {})
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """验证API密钥是否配置"""
        results = {}
        
        for platform_name, platform_config in self.config.get("platforms", {}).items():
            if not platform_config.get("enabled", False):
                results[platform_name] = False
                continue
            
            api_key_env = platform_config.get("api_key_env")
            if api_key_env:
                results[platform_name] = bool(os.getenv(api_key_env))
            else:
                # 特殊处理Reddit（需要多个环境变量）
                if platform_name == "reddit":
                    client_id = os.getenv("REDDIT_CLIENT_ID")
                    client_secret = os.getenv("REDDIT_CLIENT_SECRET")
                    results[platform_name] = bool(client_id and client_secret)
                else:
                    results[platform_name] = True
        
        return results
    
    def print_status(self):
        """打印配置状态"""
        print("\n=== 社交媒体配置状态 ===")
        
        # API密钥状态
        api_status = self.validate_api_keys()
        print("\n📋 API密钥状态:")
        for platform, status in api_status.items():
            status_icon = "✅" if status else "❌"
            enabled_icon = "🟢" if self.is_platform_enabled(platform) else "🔴"
            print(f"  {enabled_icon} {platform.capitalize()}: {status_icon}")
        
        # 数据存储设置
        storage_config = self.config.get("data_storage", {})
        print(f"\n📁 数据存储目录: {storage_config.get('base_directory', 'social_media_data')}")
        
        # 爬取设置
        scraping_config = self.get_scraping_settings()
        print(f"⚙️  每平台限制: {scraping_config.get('default_limit_per_platform', 50)} 条")
        print(f"⏱️  请求延迟: {scraping_config.get('request_delay_seconds', 2)} 秒")
        
        print("=" * 30)


# 全局配置实例
social_media_config = SocialMediaConfig()


def main():
    """测试配置"""
    config = SocialMediaConfig()
    config.print_status()
    
    # 保存默认配置
    config.save_config()


if __name__ == "__main__":
    main()
