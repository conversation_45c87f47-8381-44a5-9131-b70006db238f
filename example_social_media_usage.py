#!/usr/bin/env python3
"""
社交媒体数据获取系统使用示例
演示如何使用模拟数据和实际数据获取功能
"""

import json
import sys
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import asdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.tools.social_media_scraper import SocialMediaPost
from src.tools.local_social_media_reader import LocalSocialMediaReader, get_local_social_media_data
from src.config.social_media_config import social_media_config


def create_sample_data():
    """创建示例社交媒体数据"""
    print("📝 创建示例社交媒体数据...")
    
    # 创建输出目录
    output_dir = Path("social_media_data")
    output_dir.mkdir(exist_ok=True)
    
    # 为NVDA创建示例数据
    ticker = "NVDA"
    ticker_dir = output_dir / f"{ticker}_social_media"
    ticker_dir.mkdir(exist_ok=True)
    
    # 创建多天的示例数据
    dates = ["2024-01-15", "2024-01-16", "2024-01-17"]
    
    for date in dates:
        # Reddit示例数据
        reddit_posts = [
            {
                "platform": "reddit",
                "post_id": f"reddit_{date}_1",
                "title": f"NVDA earnings discussion - {date}",
                "content": "What do you think about NVDA's latest earnings? The AI chip demand is incredible!",
                "author": "investor123",
                "created_time": f"{date}T09:30:00",
                "url": f"https://reddit.com/r/stocks/comments/{date}_1",
                "upvotes": 250,
                "comments_count": 45,
                "ticker": ticker,
                "sentiment": "bullish",
                "engagement_score": 295.0,
                "source_subreddit": "stocks",
                "hashtags": None
            },
            {
                "platform": "reddit",
                "post_id": f"reddit_{date}_2",
                "title": f"NVIDIA GPU shortage concerns - {date}",
                "content": "Anyone else worried about the GPU shortage affecting NVDA's supply chain?",
                "author": "tech_analyst",
                "created_time": f"{date}T14:20:00",
                "url": f"https://reddit.com/r/investing/comments/{date}_2",
                "upvotes": 120,
                "comments_count": 28,
                "ticker": ticker,
                "sentiment": "bearish",
                "engagement_score": 148.0,
                "source_subreddit": "investing",
                "hashtags": None
            }
        ]
        
        # Twitter示例数据
        twitter_posts = [
            {
                "platform": "twitter",
                "post_id": f"twitter_{date}_1",
                "title": "",
                "content": f"$NVDA breaking new highs! AI revolution is just getting started 🚀 #{date} #NVDA #AI",
                "author": "tech_trader",
                "created_time": f"{date}T10:15:00",
                "url": f"https://twitter.com/i/status/{date}_1",
                "upvotes": 180,
                "comments_count": 25,
                "ticker": ticker,
                "sentiment": "bullish",
                "engagement_score": 230.0,
                "source_subreddit": None,
                "hashtags": ["NVDA", "AI"]
            },
            {
                "platform": "twitter",
                "post_id": f"twitter_{date}_2",
                "title": "",
                "content": f"$NVDA valuation looking stretched. Time to take profits? 📉 #{date} #NVDA",
                "author": "value_investor",
                "created_time": f"{date}T16:45:00",
                "url": f"https://twitter.com/i/status/{date}_2",
                "upvotes": 95,
                "comments_count": 18,
                "ticker": ticker,
                "sentiment": "bearish",
                "engagement_score": 113.0,
                "source_subreddit": None,
                "hashtags": ["NVDA"]
            }
        ]
        
        # 保存Reddit数据
        reddit_file = ticker_dir / f"reddit_{date}.json"
        with open(reddit_file, 'w', encoding='utf-8') as f:
            json.dump(reddit_posts, f, ensure_ascii=False, indent=2)
        
        # 保存Twitter数据
        twitter_file = ticker_dir / f"twitter_{date}.json"
        with open(twitter_file, 'w', encoding='utf-8') as f:
            json.dump(twitter_posts, f, ensure_ascii=False, indent=2)
        
        # 保存汇总数据
        all_posts = reddit_posts + twitter_posts
        summary_file = ticker_dir / f"all_platforms_{date}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(all_posts, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已创建 {date} 的示例数据 ({len(all_posts)} 条帖子)")
    
    print(f"📁 示例数据保存在: {ticker_dir}")
    return ticker, dates


def demonstrate_data_reading():
    """演示数据读取功能"""
    print("\n📖 演示数据读取功能...")
    
    reader = LocalSocialMediaReader()
    ticker = "NVDA"
    
    # 1. 读取单日数据
    print(f"\n1️⃣ 读取 {ticker} 在 2024-01-15 的数据:")
    posts = reader.read_multi_platform_data(ticker, "2024-01-15")
    print(f"   总共读取到 {len(posts)} 条帖子")
    
    for post in posts[:2]:  # 显示前2条
        print(f"   - {post.platform.capitalize()}: {post.title or post.content[:50]}...")
        print(f"     点赞: {post.upvotes}, 评论: {post.comments_count}, 情感: {post.sentiment}")
    
    # 2. 读取日期范围数据
    print(f"\n2️⃣ 读取 {ticker} 在 2024-01-15 到 2024-01-17 的数据:")
    range_posts = reader.read_date_range_data(ticker, "2024-01-15", "2024-01-17")
    print(f"   总共读取到 {len(range_posts)} 条帖子")
    
    # 按平台统计
    platform_stats = {}
    for post in range_posts:
        platform = post.platform
        if platform not in platform_stats:
            platform_stats[platform] = 0
        platform_stats[platform] += 1
    
    for platform, count in platform_stats.items():
        print(f"   - {platform.capitalize()}: {count} 条")
    
    # 3. 获取数据摘要
    print(f"\n3️⃣ {ticker} 数据摘要:")
    summary = reader.get_data_summary(ticker)
    print(f"   - 数据目录: {summary['data_directory']}")
    print(f"   - 总文件数: {summary['total_files']}")
    print(f"   - 日期范围: {summary['date_range']['start']} 到 {summary['date_range']['end']}")
    print(f"   - 平台文件数: {summary['platforms']}")


def demonstrate_analyst_integration():
    """演示与social_media_analyst的集成"""
    print("\n🔗 演示与social_media_analyst的集成...")
    
    # 使用便捷函数获取数据
    posts = get_local_social_media_data(
        ticker="NVDA",
        date="2024-01-15",
        platforms=["reddit", "twitter"],
        limit=50
    )
    
    print(f"📊 获取到 {len(posts)} 条帖子用于分析")
    
    # 模拟social_media_analyst的数据处理逻辑
    social_data = {
        "posts_data": {
            "total_posts": len(posts),
            "platform_breakdown": {},
            "sentiment_distribution": {"bullish": 0, "bearish": 0, "neutral": 0},
            "engagement_metrics": {
                "total_upvotes": 0,
                "total_comments": 0,
                "avg_engagement": 0
            },
            "content_analysis": {
                "top_keywords": [],
                "avg_content_length": 0
            }
        }
    }
    
    # 统计各平台数据
    for post in posts:
        platform = post.platform
        if platform not in social_data["posts_data"]["platform_breakdown"]:
            social_data["posts_data"]["platform_breakdown"][platform] = 0
        social_data["posts_data"]["platform_breakdown"][platform] += 1
        
        # 情感统计
        sentiment = post.sentiment or "neutral"
        if sentiment in social_data["posts_data"]["sentiment_distribution"]:
            social_data["posts_data"]["sentiment_distribution"][sentiment] += 1
        
        # 参与度统计
        social_data["posts_data"]["engagement_metrics"]["total_upvotes"] += post.upvotes
        social_data["posts_data"]["engagement_metrics"]["total_comments"] += post.comments_count
    
    # 计算平均参与度
    if posts:
        total_engagement = sum(p.engagement_score or 0 for p in posts)
        social_data["posts_data"]["engagement_metrics"]["avg_engagement"] = total_engagement / len(posts)
        
        total_content_length = sum(len(p.content) for p in posts)
        social_data["posts_data"]["content_analysis"]["avg_content_length"] = total_content_length / len(posts)
    
    # 显示分析结果
    print("\n📈 社交媒体分析结果:")
    print(f"   总帖子数: {social_data['posts_data']['total_posts']}")
    print(f"   平台分布: {social_data['posts_data']['platform_breakdown']}")
    print(f"   情感分布: {social_data['posts_data']['sentiment_distribution']}")
    print(f"   总点赞数: {social_data['posts_data']['engagement_metrics']['total_upvotes']}")
    print(f"   总评论数: {social_data['posts_data']['engagement_metrics']['total_comments']}")
    print(f"   平均参与度: {social_data['posts_data']['engagement_metrics']['avg_engagement']:.1f}")
    print(f"   平均内容长度: {social_data['posts_data']['content_analysis']['avg_content_length']:.0f} 字符")
    
    # 生成交易信号建议
    sentiment_dist = social_data['posts_data']['sentiment_distribution']
    bullish_ratio = sentiment_dist['bullish'] / max(1, sum(sentiment_dist.values()))
    
    if bullish_ratio > 0.6:
        signal_suggestion = "看涨"
    elif bullish_ratio < 0.4:
        signal_suggestion = "看跌"
    else:
        signal_suggestion = "中性"
    
    print(f"\n💡 基于社交媒体情感的信号建议: {signal_suggestion}")
    print(f"   (看涨比例: {bullish_ratio:.1%})")


def main():
    """主函数"""
    print("🚀 社交媒体数据获取系统使用示例")
    print("=" * 50)
    
    # 1. 创建示例数据
    ticker, dates = create_sample_data()
    
    # 2. 演示数据读取
    demonstrate_data_reading()
    
    # 3. 演示分析师集成
    demonstrate_analyst_integration()
    
    print("\n" + "=" * 50)
    print("✅ 示例演示完成!")
    print("\n📋 接下来您可以:")
    print("   1. 配置真实的API密钥来获取实时数据")
    print("   2. 使用命令行工具批量获取数据")
    print("   3. 将数据集成到social_media_analyst中")
    print("   4. 自定义搜索关键词和过滤规则")
    
    print(f"\n📁 示例数据位置: social_media_data/{ticker}_social_media/")
    print("🔧 配置文件: social_media_config.json")
    print("📖 详细文档: SOCIAL_MEDIA_SCRAPER_README.md")


if __name__ == "__main__":
    main()
